import { OrderPayload, PaymentResponse } from '@/src/app/_interfaces';
import { axiosInstance, CheckoutFormSchema, getValidBrazilianDDD } from '@/src/app/_utils';

/**
 * Formata uma data no formato YYYY-MM-DD usando o fuso horário local
 * @param date Objeto Date a ser formatado
 * @returns String no formato YYYY-MM-DD
 */
function formatLocalDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // +1 porque getMonth() retorna 0-11
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export class PaymentService {
  // Caminho relativo para evitar CORS (Next.js API Route: /api/checkout)
  private static readonly API_URL = '/api/checkout';

  static async createOrder(
    formData: CheckoutFormSchema & {
      serviceId?: string | number;
      priceId?: number;
    }
  ): Promise<PaymentResponse> {
    try {
      if (!formData.priceId && !formData.serviceId) {
        throw new Error(
          'Missing required price information: priceId or serviceId must be provided'
        );
      }

      const priceId =
        formData.priceId ||
        (formData.serviceId ? parseInt(formData.serviceId.toString(), 10) : undefined);

      if (priceId === undefined) {
        throw new Error('Failed to determine valid price ID');
      }

      const baseUrl = window.location.origin;
      const successUrl = `${baseUrl}/success`;

      const phoneDigits = formData.phone.replace(/\D/g, '');
      const extractedDDD = phoneDigits.substring(0, 2);
      const phoneWithoutDDD = phoneDigits.substring(2);
      const ddd = getValidBrazilianDDD(extractedDDD);

      // Criando data no formato local (YYYY-MM-DD)
      const dateLocal = new Date();
      const orderDateLocal = formatLocalDate(dateLocal);

      // Data agendada
      const scheduledDateUTC = formData.date.toISOString().split('T')[0];

      const payload: OrderPayload = {
        priceId: priceId,
        phoneCode: ddd,
        phoneNumber: phoneWithoutDDD,
        email: formData.email,
        complement: formData.complement || '',
        modality: 'RESIDENCIAL',
        orderDate: orderDateLocal, // Usando data local em vez de UTC
        // Mantendo o formato original para a data agendada para compatibilidade com os testes
        scheduledDate: scheduledDateUTC, // Usando UTC para manter compatibilidade com os testes
        scheduledPeriod: formData.period === 'morning' ? 'MANHA' : 'TARDE',
        personTypeId: 1,
        firstName: formData.firstName,
        lastName: formData.lastName,
        socialName: `${formData.firstName} ${formData.lastName}`,
        identificationNumber: formData.cpf.replace(/[.-]/g, ''),
        numberAd: formData.streetNumber,
        successUrl: successUrl,
        zipCode: formData.cep.replace(/-/g, ''),
        serviceId: formData.serviceId,
      };

      const { data } = await axiosInstance.post<PaymentResponse>(this.API_URL, payload);

      const successUrlWithOrderId = `${successUrl}?orderId=${data.orderId}`;
      const finalUrl = data.secureUrl
        ? data.secureUrl.replace(successUrl, successUrlWithOrderId)
        : successUrlWithOrderId;

      return {
        orderId: data.orderId,
        secureUrl: finalUrl,
        errors: data.errors,
        secureId: data.secureId,
      };
    } catch (error) {
      console.error('Error creating order:', error);
      throw error;
    }
  }
}
