import { countries } from '@/src/app/_utils/countries';
import { isValidBrazilianDDD } from '@/src/app/_utils/validDdds';
import { CountryCode, parsePhoneNumberFromString } from 'libphonenumber-js';

/**
 * Interface for phone validation result
 */
export interface PhoneValidationResult {
  isValid: boolean;
  error: string | null;
  formattedPhone?: string;
}

/**
 * Validates a phone number for a specific country
 * @param phoneNumber The phone number to validate
 * @param countryCode The country code (e.g., '+55' for Brazil)
 * @returns A validation result object
 */
export function validatePhoneNumber(
  phoneNumber: string | undefined,
  countryCode: string = '+55'
): PhoneValidationResult {
  // Handle empty input
  if (!phoneNumber?.trim()) {
    return {
      isValid: false,
      error: 'Insira um número de telefone válido com DDD.',
    };
  }

  // Find country information
  const country = countries.find((c) => c.code === countryCode);
  if (!country) {
    return {
      isValid: false,
      error: 'Código do país inválido.',
    };
  }

  // Extract digits only
  const digits = phoneNumber.replace(/\D/g, '');

  // Special validation for Brazilian numbers
  if (countryCode === '+55') {
    // Check if the phone number has the correct number of digits
    // Brazilian numbers can be 10 digits (landline) or 11 digits (mobile)
    if (digits.length !== 10 && digits.length !== 11) {
      return {
        isValid: false,
        error: 'O número de telefone deve ter 10 ou 11 dígitos, incluindo o DDD.',
      };
    }

    // Validate DDD
    const ddd = digits.substring(0, 2);
    if (!isValidBrazilianDDD(ddd)) {
      return {
        isValid: false,
        error: 'O DDD informado não é válido no Brasil.',
      };
    }

    // For Brazilian numbers, we'll be more lenient with the libphonenumber validation
    // since it can sometimes reject valid Brazilian numbers due to formatting differences
    return {
      isValid: true,
      error: null,
      formattedPhone: formatBrazilianPhoneNumber(digits),
    };
  }

  // For non-Brazilian numbers, use libphonenumber-js for validation
  try {
    const parsedPhoneNumber = parsePhoneNumberFromString(phoneNumber, country.iso as CountryCode);
    if (!parsedPhoneNumber || !parsedPhoneNumber.isValid()) {
      return {
        isValid: false,
        error: 'Insira um número de telefone válido.',
      };
    }

    return {
      isValid: true,
      error: null,
      formattedPhone: parsedPhoneNumber.formatInternational(),
    };
  } catch (error) {
    console.error('Error validating phone number:', error);
    return {
      isValid: false,
      error: 'Insira um número de telefone válido.',
    };
  }
}

/**
 * Formats a Brazilian phone number
 * @param digits The digits of the phone number
 * @returns A formatted phone number string
 */
export function formatBrazilianPhoneNumber(digits: string): string {
  // Ensure we never format more than 11 digits for Brazilian numbers
  const limitedDigits = digits.substring(0, 11);

  if (limitedDigits.length === 11) {
    // Mobile format: (XX) XXXXX-XXXX
    return `(${limitedDigits.substring(0, 2)}) ${limitedDigits.substring(2, 7)}-${limitedDigits.substring(7, 11)}`;
  } else if (limitedDigits.length === 10) {
    // Landline format: (XX) XXXX-XXXX
    return `(${limitedDigits.substring(0, 2)}) ${limitedDigits.substring(2, 6)}-${limitedDigits.substring(6, 10)}`;
  } else if (limitedDigits.length > 2) {
    // Partial number
    return `(${limitedDigits.substring(0, 2)}) ${limitedDigits.substring(2)}`;
  } else if (limitedDigits.length > 0) {
    // Just the DDD
    return `(${limitedDigits})`;
  }
  return '';
}

/**
 * Formats a phone number as the user types
 * @param input The current input value
 * @param countryCode The country code
 * @returns A formatted phone number string
 */
export function formatPhoneInput(input: string, countryCode: string = '+55'): string {
  if (!input) return '';

  // For Brazilian numbers, use our custom formatter
  if (countryCode === '+55') {
    const digits = input.replace(/\D/g, '');
    // Limit to 11 digits for Brazilian numbers
    const limitedDigits = digits.substring(0, 11);
    return formatBrazilianPhoneNumber(limitedDigits);
  }

  // For other countries, just return the input as is
  return input;
}
