'use client';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  AskForService,
  Button,
  Icon,
  Loader,
  Separator,
  ServiceNavigationMenuDesktop,
} from '@/src/app/_components';
import { MarkdownRenderer } from '@/src/app/_components/Pages/Service/MarkdownRenderer';
import {
  useServiceContext,
  Category,
  Service as ContextService,
} from '@/src/app/_context/ServiceContext';
import { useInputFormat, useOrderData, useTrackPurchaseEvent } from '@/src/app/_hooks/';
import { ApiOrderResponse } from '@/src/app/_interfaces';
import { formatDate, formatPeriod } from '@/src/app/_utils/';
import { Calendar, CircleCheck, CreditCard, MapPin, User } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useCallback, useEffect } from 'react';

// Props interface for the SuccessContent component
interface SuccessContentProps {
  orderIdentifier?: string;
  servicesOverride?: Category[];
}

// Client component that uses useSearchParams
export function SuccessContent({
  orderIdentifier: propOrderIdentifier,
  servicesOverride,
}: SuccessContentProps = {}) {
  const searchParams = useSearchParams();
  // Check for both parameter names for backward compatibility
  const uuid = searchParams.get('uuid');
  const orderId = searchParams.get('orderId');
  // Use the first one that's available or the prop value if provided
  const orderIdentifier = propOrderIdentifier || uuid || orderId;

  const { capitalizeWords } = useInputFormat();
  const { services: contextServices } = useServiceContext();
  const { trackPurchaseEvent } = useTrackPurchaseEvent();

  // Use servicesOverride for tests if provided, otherwise use context services
  const services = servicesOverride || contextServices;

  // Keep the refactored hook usage
  const { orderData, isLoading, error } = useOrderData({ orderId: orderIdentifier });

  // Tracking principal de purchase, verifica se o evento ja foi enviado para evitar triggers desnecessários do evento.
  const handlePurchaseTracking = useCallback(
    (orderData: ApiOrderResponse) => {
      const serviceId = orderData?.service?.id;
      if (!serviceId) return;

      const purchaseKey = `purchase_sent_${serviceId}`;

      const alreadyTracked = sessionStorage.getItem(purchaseKey);

      if (!alreadyTracked) {
        // Pass the orderIdentifier as transaction_id to prevent duplicate events
        // Only pass orderIdentifier if it's not null
        trackPurchaseEvent(orderData, orderIdentifier || undefined);
        sessionStorage.setItem(purchaseKey, 'true');
      }
    },
    [trackPurchaseEvent, orderIdentifier]
  );

  // Combined useEffect for all analytics tracking to ensure hooks are called in the same order
  useEffect(() => {
    if (orderData?.service?.id) {
      handlePurchaseTracking(orderData);
    }
  }, [orderData, handlePurchaseTracking]);

  if (isLoading) {
    return <Loader />;
  }

  if (error) {
    return (
      <div className="container mx-auto max-w-4xl px-4 py-12">
        <div className="rounded-lg border border-red-200 bg-red-50 p-8 text-center">
          <h1 className="mb-4 text-2xl font-bold text-red-700">Erro</h1>
          <p className="mb-6 text-red-600">{error}</p>
          <Button asChild>
            <Link href="/">Voltar para a página inicial</Link>
          </Button>
        </div>
      </div>
    );
  }

  if (!orderData) {
    return null;
  }

  return (
    <div className="mx-auto w-full py-12">
      <div className="space-y-12">
        <header className="mx-auto flex w-11/12 max-w-6xl items-start space-x-4 md:w-full">
          <CircleCheck className="mt-1 h-12 w-12 flex-shrink-0 text-gray-500" aria-hidden="true" />
          <div>
            <h1 className="mb-2 text-3xl font-bold">Agendamento realizado com sucesso!</h1>
            <p className="text-muted-foreground">Aqui estão os detalhes do seu agendamento:</p>
          </div>
        </header>

        <section
          aria-labelledby="service-details"
          className="mx-auto w-11/12 max-w-6xl rounded-lg bg-primary/5 p-8 md:w-full"
        >
          <div className="flex w-full flex-col md:flex-row md:gap-10 md:text-start">
            <div className="md:w-1/2">
              {/* Aqui vamos buscar o category e subcategory com base no slug do serviço */}
              {(() => {
                const matchedCategory = services?.find((category: Category) =>
                  category.subcategories?.some((sub) =>
                    sub.services?.some(
                      (service: ContextService) => service.slug === orderData?.service?.slug
                    )
                  )
                );

                const matchedSubcategory = matchedCategory?.subcategories?.find((sub) =>
                  sub.services?.some(
                    (service: ContextService) => service.slug === orderData?.service?.slug
                  )
                );

                return (
                  <>
                    {matchedCategory && matchedSubcategory && (
                      <div className="mb-4">
                        <h2 className="text-xl font-semibold text-muted-foreground">
                          {matchedCategory.name}
                        </h2>
                        <h1 className="text-3xl font-bold">{matchedSubcategory.name}</h1>
                      </div>
                    )}
                  </>
                );
              })()}

              <Separator className="my-6" />
              <h2 id="service-details" className="text-2xl font-semibold">
                {orderData?.service?.name}
              </h2>
              <p className="mb-7 mt-4 text-muted-foreground">
                {orderData?.service?.description || ''}
              </p>
            </div>

            <div className="mb-6 flex flex-col items-start justify-start gap-5 text-sm text-muted-foreground md:w-1/2">
              <h3 className="mb-2 text-lg font-bold text-black">Condições</h3>
              <Accordion type="single" collapsible defaultValue="details" className="w-full">
                <AccordionItem value="details">
                  <AccordionTrigger className="text-sm font-bold">
                    O que está incluso
                  </AccordionTrigger>
                  <AccordionContent>
                    {orderData?.service?.details && (
                      <MarkdownRenderer
                        className="list-disc text-[#020618]"
                        markdown={orderData?.service?.details || ''}
                      />
                    )}
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="preparations">
                  <AccordionTrigger className="text-sm font-bold">Preparação</AccordionTrigger>
                  <AccordionContent>
                    <MarkdownRenderer
                      markdown={orderData?.service?.preparations || ''}
                      className="text-[#020618]"
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="serviceLimits">
                  <AccordionTrigger className="text-sm font-bold">Restrições</AccordionTrigger>
                  <AccordionContent>
                    <MarkdownRenderer
                      markdown={orderData?.service?.serviceLimits || ''}
                      className="text-[#020618]"
                    />
                  </AccordionContent>
                </AccordionItem>
              </Accordion>

              <Link
                href={orderData?.service?.termsConditionsUrl || ''}
                target="_blank"
                className="mt-4 flex items-center gap-2 text-base font-semibold text-muted-foreground"
              >
                Ver condições gerais
                <Icon name="ExternalLink" className="h-4 w-4" />
              </Link>
            </div>
          </div>

          <Separator className="my-6" />
          <div className="flex items-center justify-between">
            <span className="text-sm font-semibold text-muted-foreground">Valor total:</span>
            <span className="text-xl font-semibold">
              R$ {orderData?.service?.price?.finalPrice?.toFixed(2).replace('.', ',') || '0,00'}
            </span>
          </div>
        </section>

        <section
          aria-labelledby="schedule-details"
          className="mx-auto grid max-w-6xl gap-6 md:grid-cols-2"
        >
          <div className="mx-auto w-11/12 space-y-10 rounded-xl border-2 p-9 md:w-full">
            <div>
              <h2 id="schedule-details" className="mb-4 text-xl font-semibold">
                Detalhes do agendamento
              </h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <Calendar className="mt-1 h-5 w-5 text-primary" aria-hidden="true" />
                  <div>
                    <h3 className="font-semibold">Data e Horário</h3>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(orderData?.appointment?.date || '')} -{' '}
                      {formatPeriod(orderData?.appointment?.period || '')}
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <MapPin className="mt-1 h-5 w-5 text-primary" aria-hidden="true" />
                  <div>
                    <h3 className="font-semibold">Endereço</h3>
                    <p className="text-sm text-muted-foreground">
                      {orderData?.address?.street || ''}, {orderData?.address?.numberAd || ''}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {orderData?.address?.neighborhood || ''} -{' '}
                      {orderData?.address?.cityName || ''}/{orderData?.address?.uf || ''}
                    </p>
                    {orderData?.address?.complement && (
                      <p className="text-sm text-muted-foreground">
                        Complemento: {orderData?.address?.complement}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-8">
              <div className="flex items-start space-x-3">
                <User className="mt-1 h-5 w-5 text-primary" aria-hidden="true" />
                <div>
                  <h3 className="font-semibold">Dados pessoais</h3>
                  <p className="text-sm text-muted-foreground">
                    Nome: {capitalizeWords(orderData?.customer?.fullName || '')}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Celular: {orderData?.customer?.phone || ''}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    E-mail: {orderData?.customer?.email || ''}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    CPF: {orderData?.customer?.document || ''}
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <CreditCard className="mt-1 h-5 w-5 text-primary" aria-hidden="true" />
                <div>
                  <h3 className="font-semibold">Pagamento</h3>
                  <p className="text-sm text-muted-foreground">
                    Total pago: R${' '}
                    {orderData?.service?.price?.finalPrice?.toFixed(2)?.replace('.', ',') || ''}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Método de pagamento:{' '}
                    {orderData?.payment?.method === 'Credit Card'
                      ? 'Cartão de Crédito'
                      : orderData?.payment?.method || ''}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="mx-auto w-11/12 space-y-6 rounded-xl border-2 p-7 md:w-full">
            <h2 className="text-xl font-semibold">Próximos passos</h2>
            <div className="flex flex-col gap-6">
              {[1, 2, 3].map((number, index) => (
                <div key={number} className="flex items-start gap-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full border border-slate-200 bg-slate-50 text-slate-600">
                    <span className="w-10 text-center text-xl font-black">{number}</span>
                  </div>
                  <p className="text-md text-muted-foreground">
                    {index === 0 &&
                      'Você receberá um e-mail de confirmação com todos os detalhes do agendamento e do serviço.'}
                    {index === 1 &&
                      `A ${capitalizeWords(orderData?.service?.provider?.name || '')} entrará em contato para compartilhar os detalhes e tirar eventuais dúvidas.`}
                    {index === 2 && 'Prepare-se para receber o serviço no dia e horário agendados.'}
                  </p>
                </div>
              ))}

              <Separator className="my-6" />

              <div className="space-y-1">
                <b className="text-md text-foreground">Precisou cancelar ou reagendar?</b>
                <p className="text-md text-muted-foreground">
                  Entre em contato com a Central de Atendimento da Europ pelo telefone 0800 202
                  4011.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* <section className="mx-auto max-w-6xl w-11/12">
          <ServiceCarousel services={services} />
        </section> */}

        <section className="my-16 border-t-[1px] border-t-gray-200 px-8">
          <div className="mx-auto w-11/12 max-w-6xl">
            <h2 className="my-8 text-3xl font-bold text-muted-foreground">O que você precisa?</h2>
            <ServiceNavigationMenuDesktop
              className="w-full lg:-ml-40"
              containerClassName="grid-cols-2 md:grid-cols-4 gap-x-10 gap-y-6"
              categoryClassName="mb-6"
              categoryTitleClassName="flex items-center gap-2 mb-2"
              subcategoryListClassName="ml-7 space-y-3"
              subcategoryLinkClassName="text-sm font-medium text-muted-foreground hover:text-gray-900"
            />
          </div>
        </section>

        <section className="relative z-40 mx-auto mb-16 max-w-7xl px-8 lg:px-12">
          <AskForService
            variant="custom"
            className="relative w-full overflow-hidden lg:mx-auto"
            containerClassName="relative z-10 flex flex-col items-start justify-between gap-10 lg:flex-row lg:items-center"
            titleClassName="text-2xl font-bold leading-tight sm:text-3xl"
            descriptionClassName=" max-w-2xl text-base sm:text-lg"
            buttonClassName="w-full rounded-xl bg-white px-6 py-4 text-base font-bold text-black transition duration-300 hover:bg-gray-100 sm:px-8 sm:py-6 sm:text-lg"
            showIcon={true}
          />
        </section>

        {/* <section className="flex justify-center md:justify-start">
          <Button asChild className='rounded-xl'>
            <Link href="/" onClick={_handleScheduleAnotherServiceClick}>
              Agendar outro serviço
            </Link>
          </Button>
        </section> */}
      </div>
    </div>
  );

  return (
    <div className="mx-auto w-full py-12">
      <div className="space-y-12">
        <header className="mx-auto flex w-11/12 max-w-6xl items-start space-x-4 md:w-full">
          <CircleCheck className="mt-1 h-12 w-12 flex-shrink-0 text-gray-500" aria-hidden="true" />
          <div>
            <h1 className="mb-2 text-3xl font-bold">Agendamento realizado com sucesso!</h1>
            <p className="text-muted-foreground">Aqui estão os detalhes do seu agendamento:</p>
          </div>
        </header>

        <section
          aria-labelledby="service-details"
          className="mx-auto w-11/12 max-w-6xl rounded-lg bg-primary/5 p-8 md:w-full"
        >
          <div className="flex w-full flex-col md:flex-row md:gap-10 md:text-start">
            <div className="md:w-1/2">
              {(() => {
                if (!orderData?.service?.slug) return null;
                const matchedCategory = services.find((category) =>
                  category.subcategories.some((subcategory) =>
                    subcategory.services.some(
                      (service) => service.slug === orderData?.service?.slug
                    )
                  )
                );

                if (!matchedCategory) {
                  return (
                    <h2 id="service-details" className="text-xl font-semibold">
                      {orderData?.service?.name}
                    </h2>
                  );
                }

                const matchedSubcategory = matchedCategory?.subcategories.find((subcategory) =>
                  subcategory.services.some((service) => service.slug === orderData?.service?.slug)
                );

                if (!matchedSubcategory) {
                  return (
                    <h2 id="service-details" className="text-xl font-semibold">
                      {orderData?.service?.name}
                    </h2>
                  );
                }

                return (
                  <>
                    <h2 id="service-details" className="text-xl font-semibold">
                      {orderData?.service?.name}
                    </h2>
                    <p className="text-sm text-muted-foreground">
                      {matchedCategory?.name} &gt; {matchedSubcategory?.name}
                    </p>
                  </>
                );
              })()}

              {orderData?.service?.description && (
                <div className="prose-sm prose-headings:text-base prose-headings:font-semibold prose-p:text-muted-foreground mt-4 max-w-none">
                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="description" className="border-none">
                      <AccordionTrigger className="py-0 text-base font-semibold hover:no-underline">
                        <div className="flex items-center">
                          <span>Ver detalhes do serviço</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="max-h-72 overflow-y-auto pr-4">
                          <MarkdownRenderer markdown={orderData?.service?.description || ''} />
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>
              )}

              <div className="mt-6 flex items-start space-x-3">
                <Calendar className="mt-1 h-5 w-5 text-primary" aria-hidden="true" />
                <div>
                  <h3 className="font-semibold">Agendamento</h3>
                  <p className="text-sm text-muted-foreground">
                    {formatDate(orderData?.appointment?.date || '')} -{' '}
                    {formatPeriod(orderData?.appointment?.period || '')}
                  </p>
                </div>
              </div>

              <div className="mt-4 flex items-start space-x-3">
                <MapPin className="mt-1 h-5 w-5 text-primary" aria-hidden="true" />
                <div>
                  <h3 className="font-semibold">Endereço</h3>
                  <p className="text-sm text-muted-foreground">
                    {orderData?.address?.street || ''}, {orderData?.address?.numberAd || ''}
                    {orderData?.address?.complement && `, ${orderData?.address?.complement}`}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {orderData?.address?.neighborhood || ''} - {orderData?.address?.cityName || ''}/
                    {orderData?.address?.uf || ''}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    CEP: {orderData?.address?.zipCode || ''}
                  </p>
                </div>
              </div>

              <div className="mt-4 flex items-start space-x-3">
                <User className="mt-1 h-5 w-5 text-primary" aria-hidden="true" />
                <div>
                  <h3 className="font-semibold">Prestador de serviço</h3>
                  <p className="text-sm text-muted-foreground">
                    {orderData?.service?.provider?.name || ''}
                  </p>
                </div>
              </div>
            </div>

            <Separator className="my-6 md:hidden" />

            <div className="md:w-1/2">
              <h3 className="font-semibold md:text-right">Código do agendamento</h3>
              <p className="text-sm text-muted-foreground md:text-right">
                {orderIdentifier || orderData?.service?.id || 'N/A'}
              </p>

              <div className="mt-4 flex items-start space-x-3">
                <CreditCard className="mt-1 h-5 w-5 text-primary" aria-hidden="true" />
                <div>
                  <h3 className="font-semibold">Pagamento</h3>
                  <p className="text-sm text-muted-foreground">
                    Total pago: R${' '}
                    {orderData?.service?.price?.finalPrice?.toFixed(2)?.replace('.', ',') || ''}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Método de pagamento:{' '}
                    {orderData?.payment?.method === 'Credit Card'
                      ? 'Cartão de Crédito'
                      : orderData?.payment?.method || ''}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="mx-auto w-11/12 space-y-6 rounded-xl border-2 p-7 md:w-full">
            <h2 className="text-xl font-semibold">Próximos passos</h2>
            <div className="flex flex-col gap-6">
              {[1, 2, 3].map((number, index) => (
                <div key={number} className="flex items-start gap-4">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full border border-slate-200 bg-slate-50 text-slate-600">
                    <span className="w-10 text-center text-xl font-black">{number}</span>
                  </div>
                  <p className="text-md text-muted-foreground">
                    {index === 0 &&
                      'Você receberá um e-mail de confirmação com todos os detalhes do agendamento e do serviço.'}
                    {index === 1 &&
                      `A ${capitalizeWords(orderData?.service?.provider?.name || '')} entrará em contato para compartilhar os detalhes e tirar eventuais dúvidas.`}
                    {index === 2 && 'Prepare-se para receber o serviço no dia e horário agendados.'}
                  </p>
                </div>
              ))}

              <Separator className="my-6" />

              <div className="space-y-1">
                <b className="text-md text-foreground">Precisou cancelar ou reagendar?</b>
                <p className="text-md text-muted-foreground">
                  Entre em contato com a Central de Atendimento da Europ pelo telefone 0800 202
                  4011.
                </p>
              </div>
            </div>
          </div>
        </section>

        <section className="my-16 border-t-[1px] border-t-gray-200 px-8">
          <div className="mx-auto w-11/12 max-w-6xl">
            <h2 className="my-8 text-3xl font-bold text-muted-foreground">O que você precisa?</h2>
            <ServiceNavigationMenuDesktop
              className="w-full lg:-ml-40"
              containerClassName="grid-cols-2 md:grid-cols-4 gap-x-10 gap-y-6"
              categoryClassName="mb-6"
              categoryTitleClassName="flex items-center gap-2 mb-2"
              subcategoryListClassName="ml-7 space-y-3"
              subcategoryLinkClassName="text-sm font-medium text-muted-foreground hover:text-gray-900"
            />
          </div>
        </section>

        <section className="relative z-40 mx-auto mb-16 max-w-7xl px-8 lg:px-12">
          <AskForService
            variant="custom"
            className="relative w-full overflow-hidden lg:mx-auto"
            containerClassName="relative z-10 flex flex-col items-start justify-between gap-10 lg:flex-row lg:items-center"
            titleClassName="text-2xl font-bold leading-tight sm:text-3xl"
            descriptionClassName=" max-w-2xl text-base sm:text-lg"
            buttonClassName="w-full rounded-xl bg-white px-6 py-4 text-base font-bold text-black transition duration-300 hover:bg-gray-100 sm:px-8 sm:py-6 sm:text-lg"
            showIcon={true}
          />
        </section>
      </div>
    </div>
  );
}
