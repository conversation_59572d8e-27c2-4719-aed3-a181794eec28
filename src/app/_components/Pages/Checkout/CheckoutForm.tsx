'use client';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  AddressForm,
  Checkbox,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Icon,
  PersonalInfoForm,
  ScheduleForm,
  ServiceSummaryCard,
} from '@/src/app/_components';

import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { useHandleBeginCheckout } from '@/src/app/_hooks/';
import type { CheckoutFormProps } from '@/src/app/_interfaces';
import { CheckoutFormSchema, checkoutFormSchema } from '@/src/app/_utils';
import { MarkdownRenderer } from '../Service/MarkdownRenderer';

export function CheckoutForm({ service, onSubmit }: CheckoutFormProps) {
  // UI state
  const [isDatePickerVisible, setIsDatePickerVisible] = useState(false);
  const [isStateSelectVisible, setIsStateSelectOpen] = useState(false);
  const [focusedBlock, setFocusedBlock] = useState<string | null>(null);
  const [formIsValid, setFormIsValid] = useState(false);

  const { trackBeginCheckout } = useHandleBeginCheckout();

  const methods = useForm<CheckoutFormSchema>({
    resolver: zodResolver(checkoutFormSchema),
    mode: 'onChange',
    defaultValues: {
      firstName: '',
      lastName: '',
      countryCode: '+55', // Default to Brazil
      phone: '',
      cpf: '',
      email: '',
      cep: '',
      street: '',
      streetNumber: '',
      complement: '',
      neighborhood: '',
      city: '',
      state: '',
      date: undefined,
      period: undefined,
      terms: false,
    },
  });

  // Set default country code
  useEffect(() => {
    methods.setValue('countryCode', '+55');
    methods.trigger('countryCode');
  }, [methods]);

  // Add effect to watch form field changes and update formIsValid
  useEffect(() => {
    // Watch all form fields including the terms checkbox
    const subscription = methods.watch((_, { name }) => {
      // Force validation when terms checkbox changes
      if (name === 'terms') {
        methods.trigger();
      }
      // Update form validity whenever anything changes
      setFormIsValid(isFormValid());
    });

    // Clean up subscription
    return () => subscription.unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [methods]);

  // Add effect to watch form state changes (errors, isDirty, etc.)
  useEffect(() => {
    // Update form validity when form state changes
    setFormIsValid(isFormValid());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [methods.formState]);

  // Form validation
  const isFormValid = () => {
    const values = methods.getValues();
    const {
      firstName,
      lastName,
      date,
      period,
      cep,
      street,
      streetNumber,
      neighborhood,
      countryCode,
      city,
      state,
      phone,
      cpf,
      email,
      terms,
    } = values;

    // Check if all required fields are filled
    const allFieldsFilled =
      !!firstName &&
      !!lastName &&
      !!date &&
      !!period &&
      !!cep &&
      !!street &&
      !!countryCode &&
      !!streetNumber &&
      !!neighborhood &&
      !!city &&
      !!state &&
      !!phone &&
      !!cpf &&
      !!email &&
      !!terms;

    // Ensure there are no validation errors
    const noValidationErrors = Object.keys(methods.formState.errors).length === 0;

    // Check if the form is valid
    const isValid = allFieldsFilled && noValidationErrors;

    // For debugging
    if (!isValid) {
      // Commented out to avoid console logs
      // console.log('Form validation failed:', {
      //   allFieldsFilled,
      //   noValidationErrors,
      //   errors: methods.formState.errors,
      //   terms,
      // });
    }

    return isValid;
  };

  const handleBeginCheckout = async () => {
    // Validate the form before proceeding
    const isValid = await methods.trigger();
    setFormIsValid(isFormValid());

    if (isValid) {
      //@ts-expect-error
      trackBeginCheckout(service);
    }
  };

  return (
    <FormProvider {...methods}>
      <div className="flex flex-col gap-12 md:grid md:grid-cols-3">
        <div className="md:order-1 md:col-span-2">
          <div>
            <h1 className="mb-8 text-2xl font-bold">Agendar serviço</h1>
            <form
              id="checkout-form"
              onSubmit={methods.handleSubmit(onSubmit)}
              className="space-y-12 px-0"
            >
              <ScheduleForm
                isDatePickerVisible={isDatePickerVisible}
                setIsDatePickerVisible={setIsDatePickerVisible}
                focusedBlock={focusedBlock}
                setFocusedBlock={setFocusedBlock}
              />

              <AddressForm
                service={service}
                isStateSelectVisible={isStateSelectVisible}
                setIsStateSelectOpen={setIsStateSelectOpen}
                focusedBlock={focusedBlock}
                setFocusedBlock={setFocusedBlock}
              />

              <PersonalInfoForm focusedBlock={focusedBlock} setFocusedBlock={setFocusedBlock} />

              <div className="w-full overflow-hidden md:hidden">
                <ServiceSummaryCard
                  service={service}
                  onSubmit={handleBeginCheckout}
                  isFormValid={formIsValid}
                  showButton={true}
                  formId="checkout-form"
                />
              </div>

              <FormField
                control={methods.control}
                name="terms"
                render={({ field }) => (
                  <FormItem className="flex flex-col rounded-md border px-8 py-8">
                    <div>
                      <h4 className="font-bold text-black">Condições do serviço:</h4>
                    </div>

                    <Accordion type="single" collapsible defaultValue="incluso" className="w-full">
                      {/* O que está incluso */}
                      <AccordionItem
                        value="incluso"
                        className="overflow-hidden border-b border-slate-200"
                      >
                        <AccordionTrigger className="py-6 text-left text-base font-semibold text-muted-foreground hover:no-underline">
                          O que está incluso
                        </AccordionTrigger>
                        <AccordionContent className="pb-6 pt-0">
                          <MarkdownRenderer className="list-disc" markdown={service.details} />
                        </AccordionContent>
                      </AccordionItem>

                      {/* Preparação */}
                      <AccordionItem
                        value="preparacao"
                        className="overflow-hidden border-b border-slate-200"
                      >
                        <AccordionTrigger className="py-6 text-left text-base font-semibold text-muted-foreground hover:no-underline">
                          Preparação
                        </AccordionTrigger>
                        <AccordionContent className="pb-6 pt-0">
                          <MarkdownRenderer className="list-disc" markdown={service.preparations} />
                        </AccordionContent>
                      </AccordionItem>

                      {/* Restrições */}
                      <AccordionItem
                        value="restricoes"
                        className="overflow-hidden border-b border-slate-200 py-0 last:mb-0"
                      >
                        <AccordionTrigger className="text-left text-base font-semibold text-muted-foreground hover:no-underline">
                          Restrições
                        </AccordionTrigger>
                        <AccordionContent className="pb-6 pt-0">
                          <MarkdownRenderer markdown={service.serviceLimits} />
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>

                    <div className="border-b border-slate-200 py-6">
                      <Link
                        href={service.termsConditionsUrl || ''}
                        target="_blank"
                        className="flex items-center gap-2 text-base font-semibold text-muted-foreground"
                      >
                        Ver condições gerais
                        <Icon name="ExternalLink" className="h-4 w-4" />
                      </Link>
                    </div>

                    <div className="flex flex-row items-center space-x-3 pt-6">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={(checked) => {
                            field.onChange(checked);
                            // Trigger validation for terms and update form validity
                            methods.trigger('terms').then(() => {
                              setFormIsValid(isFormValid());
                            });
                          }}
                          required
                          id="terms-checkbox"
                        />
                      </FormControl>
                      <div className="flex flex-col">
                        <label htmlFor="terms-checkbox" className="text-base">
                          Confirmo que estou de acordo com as condições descritas acima.
                        </label>
                        <FormMessage />
                      </div>
                    </div>
                  </FormItem>
                )}
              />

              {/* Button moved to ServiceSummaryCard */}
            </form>
          </div>
        </div>

        <div className="hidden md:order-2 md:col-span-1 md:block">
          <div className="sticky top-28">
            <ServiceSummaryCard
              service={service}
              onSubmit={handleBeginCheckout}
              isFormValid={formIsValid}
              showButton={true}
              formId="checkout-form"
            />
          </div>
        </div>
      </div>
    </FormProvider>
  );
}
