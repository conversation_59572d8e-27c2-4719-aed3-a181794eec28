'use client';

import { <PERSON><PERSON>, Card, CardContent, Separator } from '@/src/app/_components';
import { ServiceType } from '@/src/app/_interfaces';
import { Calendar, Check, ChevronRight } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

interface ServiceSummaryCardProps {
  service: ServiceType | null;
  onSubmit?: () => void;
  isFormValid?: boolean;
  showButton?: boolean;
  formId?: string;
}

export function ServiceSummaryCard({
  service,
  onSubmit,
  isFormValid = true,
  showButton = false,
  formId,
}: ServiceSummaryCardProps) {
  if (!service) {
    return (
      <Card className="h-fit p-2 shadow-xl">
        <CardContent className="p-4 px-6 sm:p-6">
          <h2 className="text-2xl font-bold">Resumo do serviço</h2>
          <div className="rounded-lg bg-red-50 p-4 text-red-700">
            <p className="text-sm">Não foi possível carregar os detalhes do serviço.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-fit p-2 shadow-xl">
      <CardContent className="overflow-y-auto overflow-x-hidden p-8">
        <h2 className="text-2xl font-bold">Resumo do serviço</h2>

        {/* Categoria, subcategoria e tipo de serviço */}
        <div className="mb-6 mt-6 rounded-lg bg-slate-100 p-6">
          {service.categoryName ? (
            <p className="text-base font-semibold text-slate-500">{service.categoryName}</p>
          ) : (
            <p className="text-base font-semibold text-slate-500">Categoria não disponível</p>
          )}
          {/* Mostrar subcategoria apenas se for diferente da categoria */}
          {service.subcategoryName && service.subcategoryName !== service.categoryName ? (
            <p className="text-lg font-semibold text-slate-900">{service.subcategoryName}</p>
          ) : null}

          <div className="flex">
            <ChevronRight color="gray" />
            <div className="text-lg font-semibold text-muted-foreground">{service.name}</div>
          </div>
        </div>

        <Separator className="my-6" />
        <div className="mb-6 flex flex-col gap-6">
          <div className="flex items-start">
            <Check className="mr-3 mt-1 h-5 w-5 text-[#FCC800]" strokeWidth={4} />
            <div>
              <span className="font-semibold">90 dias de Garantia</span>
              <p className="text-sm text-muted-foreground">
                Garantia de 90 dias em todos os serviços realizados
              </p>
            </div>
          </div>
          <div className="flex items-start">
            <Check className="mr-3 mt-1 h-5 w-5 text-[#FCC800]" strokeWidth={4} />
            <div>
              <span className="font-semibold">Agendamento imediato</span>
              <p className="text-sm text-muted-foreground">
                Escolha o melhor dia e a hora do seu atendimento.
              </p>
            </div>
          </div>
        </div>

        {service.provider && (
          <Link
            href={service.provider.providerUrl}
            className="mb-6 flex items-start rounded-lg bg-gray-50 p-6 hover:bg-gray-200"
            target="_blank"
            rel="noopener noreferrer"
          >
            <div className="mr-3">
              {service.provider.imageUrl ? (
                <Image
                  src={service.provider.imageUrl}
                  alt={service.provider.name}
                  width={40}
                  height={40}
                  className="rounded-full"
                />
              ) : (
                <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                  <span className="text-sm font-medium text-muted-foreground">
                    {service.provider.name.charAt(0)}
                  </span>
                </div>
              )}
            </div>
            <div>
              <h4 className="font-semibold">{service.provider.name}</h4>
              <p className="text-sm text-gray-500">{service.provider.description || ''}</p>
            </div>
          </Link>
        )}

        <Separator className="my-6" />
        <div className="mt-6">
          <p className="text-lg font-semibold text-muted-foreground line-through">
            R$ {service.price.originalPrice.toFixed(2).replace('.', ',')}
          </p>
          <p className="text-3xl font-bold text-primary">
            R$ {service.price.finalPrice.toFixed(2).replace('.', ',')}
          </p>
          {showButton && (
            <Button
              type="submit"
              form={formId}
              disabled={!isFormValid}
              className="mt-8 w-full rounded-xl"
              onClick={onSubmit}
            >
              <Calendar className="mr-2 h-4 w-4" />
              Continuar agendamento
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
