/**
 * @jest-environment jsdom
 */

import { axiosInstance } from '@/src/app/_utils';
import { getServices } from '@/src/app/layout';
import { AxiosError } from 'axios';

// Mock axios
jest.mock('@/src/app/_utils', () => ({
  axiosInstance: {
    get: jest.fn(),
  },
}));

describe('Layout', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getServices', () => {
    it('fetches services correctly', async () => {
      // Mock successful API response
      const mockServices = [
        {
          id: '1',
          name: 'Category 1',
          subcategories: [
            {
              id: '1-1',
              name: 'Subcategory 1',
              services: [
                {
                  id: '1-1-1',
                  name: 'Service 1',
                },
              ],
            },
          ],
        },
      ];

      (axiosInstance.get as jest.Mock).mockResolvedValue({
        data: {
          categories: mockServices,
        },
      });

      // Call getServices
      const services = await getServices();

      // Check if API was called correctly
      expect(axiosInstance.get).toHaveBeenCalledWith('/service-type/list', {
        headers: {
          'service-provider': 'EUR',
        },
      });

      // Check if services were returned correctly
      expect(services).toEqual(mockServices);
    });

    it('handles API error correctly', async () => {
      // Mock API error
      (axiosInstance.get as jest.Mock).mockRejectedValue(new AxiosError('API Error', '500'));

      // Call getServices and expect it to throw
      await expect(getServices()).rejects.toThrow('API error: API Error');
    });

    it('handles non-Axios errors correctly', async () => {
      // Mock a generic error
      (axiosInstance.get as jest.Mock).mockRejectedValue(new Error('Generic Error'));

      // Call getServices and expect it to throw
      await expect(getServices()).rejects.toThrow('Generic Error');
    });
  });
});
