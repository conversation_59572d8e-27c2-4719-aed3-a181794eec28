import '@testing-library/jest-dom';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';

// Mock das funções e componentes
jest.mock('@/src/app/_functions/analytics/common/trackClickEvent', () => ({
  trackClickEvent: jest.fn(),
}));

jest.mock('@/src/app/_components', () => ({
  Icon: ({ name, className }: any) => <span data-testid={`icon-${name}`} className={className} />,
  Button: ({ children, className, ...props }: any) => (
    <button className={className} {...props}>
      {children}
    </button>
  ),
}));

// Mock do ServiceNavigationMenuDesktop
jest.mock('@/src/app/_components/Common/Navigation/ServiceNavigationMenuDesktop', () => ({
  ServiceNavigationMenuDesktop: () => <div data-testid="service-navigation-menu-desktop" />,
}));

// Mock do dynamic import
jest.mock('next/dynamic', () => () => {
  const DynamicComponent = ({ children }: any) => children;
  DynamicComponent.displayName = 'MockedDynamicComponent';
  return DynamicComponent;
});

// Mock do Link do Next.js
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ children, ...props }: any) => <a {...props}>{children}</a>,
}));

// Importar o componente depois dos mocks
import DesktopNavigation from '@/src/app/_components/Common/Header/DesktopNavigation';

describe('DesktopNavigation Component', () => {
  // Resetar todos os mocks antes de cada teste
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  const mockProps = {
    submenuRef: { current: null },
    isSubmenuOpen: false,
    handleMouseEnter: jest.fn(),
    handleMouseLeave: jest.fn(),
    pathname: '/test-path',
  };

  it('renders the DesktopNavigation component correctly', () => {
    render(<DesktopNavigation {...mockProps} />);

    // Verificar se o menu de serviços está presente
    expect(screen.getByText('Serviços disponíveis')).toBeInTheDocument();
    expect(screen.getByTestId('icon-ChevronDown')).toBeInTheDocument();

    // Verificar se o link para outros serviços está presente
    expect(screen.getByText('Outros serviços')).toBeInTheDocument();
    expect(screen.getByText('Outros serviços')).toHaveAttribute(
      'href',
      'https://www.getninjas.com.br/'
    );
  });

  it('shows service menu on mouse enter and hides on mouse leave', async () => {
    render(<DesktopNavigation {...mockProps} />);

    // Inicialmente o menu não deve estar visível
    expect(screen.queryByTestId('service-navigation-menu-desktop')).not.toBeInTheDocument();

    // Simular mouse enter no botão de serviços
    const servicesButton = screen.getByText('Serviços disponíveis').parentElement;
    fireEvent.mouseEnter(servicesButton!);

    // Verificar se o menu ficou visível
    expect(screen.getByTestId('service-navigation-menu-desktop')).toBeInTheDocument();

    // Simular mouse leave
    fireEvent.mouseLeave(servicesButton!);

    // Avançar o timer para que o setTimeout execute
    act(() => {
      jest.advanceTimersByTime(200);
    });

    // Verificar se o menu foi fechado
    await waitFor(() => {
      expect(screen.queryByTestId('service-navigation-menu-desktop')).not.toBeInTheDocument();
    });
  });

  it('tracks click on "Outros serviços" link', () => {
    const { trackClickEvent } = require('@/src/app/_functions/analytics/common/trackClickEvent');

    render(<DesktopNavigation {...mockProps} />);

    // Clicar no link "Outros serviços"
    fireEvent.click(screen.getByText('Outros serviços'));

    // Verificar se o evento de analytics foi disparado
    expect(trackClickEvent).toHaveBeenCalledWith('GetNinjas Core');
  });

  it('handles mouse enter on dropdown menu', () => {
    render(<DesktopNavigation {...mockProps} />);

    // Primeiro mostrar o menu
    const servicesButton = screen.getByText('Serviços disponíveis').parentElement;
    fireEvent.mouseEnter(servicesButton!);

    // Pegar a referência ao dropdown
    const dropdown = screen.getByTestId('service-navigation-menu-desktop').parentElement;

    // Simular mouse leave no botão
    fireEvent.mouseLeave(servicesButton!);

    // Imediatamente simular mouse enter no dropdown
    fireEvent.mouseEnter(dropdown!);

    // Avançar o timer um pouco
    act(() => {
      jest.advanceTimersByTime(50);
    });

    // Menu deve continuar visível
    expect(screen.getByTestId('service-navigation-menu-desktop')).toBeInTheDocument();
  });

  it('toggles the chevron icon when menu is opened/closed', async () => {
    render(<DesktopNavigation {...mockProps} />);

    const chevronIcon = screen.getByTestId('icon-ChevronDown');

    // Inicialmente o ícone não deve ter a classe rotate-180
    expect(chevronIcon).not.toHaveClass('rotate-180');

    // Abrir o menu
    const servicesButton = screen.getByText('Serviços disponíveis').parentElement;
    fireEvent.mouseEnter(servicesButton!);

    // O ícone deve ter a classe rotate-180
    expect(chevronIcon).toHaveClass('rotate-180');

    // Fechar o menu
    fireEvent.mouseLeave(servicesButton!);

    // Avançar o timer
    act(() => {
      jest.advanceTimersByTime(200);
    });

    // O ícone não deve ter a classe rotate-180
    await waitFor(() => {
      expect(chevronIcon).not.toHaveClass('rotate-180');
    });
  });
});
