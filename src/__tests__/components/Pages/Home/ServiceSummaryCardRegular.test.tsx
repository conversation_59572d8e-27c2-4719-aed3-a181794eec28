import { ServiceSummaryCard } from '@/src/app/_components/Pages/Home/ServiceSummaryCard';
import { render, screen, fireEvent } from '@testing-library/react';

// Mock the components used in ServiceSummaryCard
jest.mock('@/src/app/_components', () => ({
  Button: ({ children, onClick, disabled, className, type, form }: any) => (
    <button
      onClick={onClick}
      disabled={disabled}
      className={className}
      type={type}
      form={form}
      data-testid="button"
    >
      {children}
    </button>
  ),
  Card: ({ children, className }: any) => (
    <div className={className} data-testid="card">
      {children}
    </div>
  ),
  CardContent: ({ children, className }: any) => (
    <div className={className} data-testid="card-content">
      {children}
    </div>
  ),
  Separator: ({ className }: any) => <hr className={className} data-testid="separator" />,
}));

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, width, height, className }: any) => (
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      data-testid="next-image"
    />
  ),
}));

// Mock next/link
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ href, children, className, target, rel }: any) => (
    <a href={href} className={className} target={target} rel={rel} data-testid="next-link">
      {children}
    </a>
  ),
}));

// Mock Lucide icons
jest.mock('lucide-react', () => ({
  Calendar: () => <span data-testid="calendar-icon">Calendar Icon</span>,
  Check: ({ className, strokeWidth }: any) => (
    <span data-testid="check-icon" className={className} style={{ strokeWidth }}>
      Check Icon
    </span>
  ),
  ChevronRight: ({ color }: any) => (
    <span data-testid="chevron-right-icon" style={{ color }}>
      ChevronRight Icon
    </span>
  ),
}));

// Mock service data
const mockService = {
  id: 1,
  name: 'Test Service',
  slug: 'test-service',
  description: 'Test service description',
  imageUrl: '/test-image.jpg',
  status: 'active',
  categoryName: 'Test Category',
  subcategoryName: 'Test Subcategory',
  price: {
    priceId: 1,
    originalPrice: 100,
    discountPrice: 20,
    finalPrice: 80,
  },
  provider: {
    id: 1,
    name: 'Test Provider',
    description: 'Test provider description',
    imageUrl: '/provider-image.jpg',
    providerUrl: 'https://provider.com',
  },
};

describe('ServiceSummaryCard (Regular)', () => {
  it('renders error state when service is null', () => {
    render(<ServiceSummaryCard service={null} />);

    expect(screen.getByText('Resumo do serviço')).toBeInTheDocument();
    expect(
      screen.getByText('Não foi possível carregar os detalhes do serviço.')
    ).toBeInTheDocument();
  });

  it('renders service details correctly', () => {
    render(<ServiceSummaryCard service={mockService} />);

    // Check title
    expect(screen.getByText('Resumo do serviço')).toBeInTheDocument();

    // Check category and subcategory
    expect(screen.getByText('Test Category')).toBeInTheDocument();
    expect(screen.getByText('Test Subcategory')).toBeInTheDocument();

    // Check service name
    expect(screen.getByText('Test Service')).toBeInTheDocument();

    // Check guarantees
    expect(screen.getByText('90 dias de Garantia')).toBeInTheDocument();
    expect(
      screen.getByText('Garantia de 90 dias em todos os serviços realizados')
    ).toBeInTheDocument();
    expect(screen.getByText('Agendamento imediato')).toBeInTheDocument();
    expect(
      screen.getByText('Escolha o melhor dia e a hora do seu atendimento.')
    ).toBeInTheDocument();

    // Check provider info
    expect(screen.getByText('Test Provider')).toBeInTheDocument();
    expect(screen.getByText('Test provider description')).toBeInTheDocument();

    // Check price
    expect(screen.getByText('R$ 100,00')).toBeInTheDocument();
    expect(screen.getByText('R$ 80,00')).toBeInTheDocument();
  });

  it('does not render subcategory when it is the same as category', () => {
    const serviceWithSameCategory = {
      ...mockService,
      subcategoryName: 'Test Category', // Same as categoryName
    };

    render(<ServiceSummaryCard service={serviceWithSameCategory} />);

    // Should only show the category name once
    const categoryElements = screen.getAllByText('Test Category');
    expect(categoryElements.length).toBe(1);
  });

  it('renders provider without image correctly', () => {
    const serviceWithoutProviderImage = {
      ...mockService,
      provider: {
        ...mockService.provider,
        imageUrl: '', // Empty image URL
      },
    };

    render(<ServiceSummaryCard service={serviceWithoutProviderImage} />);

    // Should show the first letter of the provider name
    expect(screen.getByText('T')).toBeInTheDocument();
  });

  it('renders the button when showButton is true', () => {
    render(<ServiceSummaryCard service={mockService} showButton={true} />);

    const button = screen.getByTestId('button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Continuar agendamento');
  });

  it('does not render the button when showButton is false', () => {
    render(<ServiceSummaryCard service={mockService} showButton={false} />);

    expect(screen.queryByTestId('button')).not.toBeInTheDocument();
  });

  it('disables the button when isFormValid is false', () => {
    render(<ServiceSummaryCard service={mockService} showButton={true} isFormValid={false} />);

    const button = screen.getByTestId('button');
    expect(button).toBeDisabled();
  });

  it('calls onSubmit when the button is clicked', () => {
    const mockOnSubmit = jest.fn();
    render(<ServiceSummaryCard service={mockService} showButton={true} onSubmit={mockOnSubmit} />);

    const button = screen.getByTestId('button');
    fireEvent.click(button);

    expect(mockOnSubmit).toHaveBeenCalledTimes(1);
  });

  it('passes formId to the button', () => {
    render(<ServiceSummaryCard service={mockService} showButton={true} formId="test-form-id" />);

    const button = screen.getByTestId('button');
    expect(button).toHaveAttribute('form', 'test-form-id');
  });
});
