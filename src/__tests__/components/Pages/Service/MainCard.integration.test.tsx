import MainCard from '@/src/app/_components/Pages/Service/MainCard';
import { ServiceType } from '@/src/app/_interfaces';
import { act, render, screen, waitFor } from '@testing-library/react';

// Mock the Next.js router
const mockRouterPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockRouterPush,
  }),
  useParams: () => ({
    slug: 'test-service',
  }),
}));

// Mock the ServiceContext
const mockUseServiceContext = jest.fn();
jest.mock('@/src/app/_context/ServiceContext', () => ({
  useServiceContext: () => mockUseServiceContext(),
}));

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    pathname: '/test-service',
    href: 'http://localhost/test-service',
    search: '',
    hash: '',
    host: 'localhost',
    hostname: 'localhost',
    protocol: 'http:',
    origin: 'http://localhost',
    port: '',
    assign: jest.fn(),
    replace: jest.fn(),
    reload: jest.fn(),
  },
  writable: true,
});

// Mock the Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, onLoad, ...props }: any) => {
    // Call onLoad immediately to simulate image loaded
    if (onLoad) setTimeout(() => onLoad({ target: { complete: true } }), 0);
    return <img src={src} alt={alt} {...props} data-testid="next-image" />;
  },
}));

// Mock the Calendar icon and other Lucide icons
jest.mock('lucide-react', () => ({
  Calendar: () => <div data-testid="calendar-icon" />,
  MapPin: () => <div data-testid="map-pin-icon" />,
  ChevronDown: ({ className }: { className?: string }) => (
    <div data-testid="chevron-down-icon" className={className} />
  ),
}));

// Mock the analytics hooks
const mockHandleAddToCart = jest.fn();
const mockSendEvent = jest.fn();
jest.mock('@/src/app/_hooks', () => ({
  useAnalyticsEventGeneric: () => ({
    sendEvent: mockSendEvent,
  }),
  useHandleAddToCart: () => ({
    handleAddToCart: mockHandleAddToCart,
  }),
}));

// Mock the Button component
jest.mock('@/src/app/_components', () => ({
  Button: ({ children, onClick, disabled, className }: any) => (
    <button onClick={onClick} disabled={disabled} className={className} data-testid="button">
      {children}
    </button>
  ),
}));

// Mock the formatPrice utility
const mockFormatPrice = jest.fn((price) => `R$ ${price},00`);
const mockConvertToServiceTypes = jest.fn((services) => services);
jest.mock('@/src/app/_utils', () => ({
  convertToServiceTypes: (services: any) => mockConvertToServiceTypes(services),
  formatPrice: (price: any) => mockFormatPrice(price),
}));

describe('MainCard Component (Integration Tests)', () => {
  // This variable will hold our real API data
  let realApiService: ServiceType | null = null;
  let realApiServices: ServiceType[] = [];

  // Create fallback mock data in case API is unavailable
  const createMockService = (id: number, name: string, slug: string): ServiceType => ({
    id,
    name,
    slug,
    description: `Description for ${name}`,
    imageUrl: `/images/${slug}.jpg`,
    status: 'active',
    price: {
      priceId: id,
      originalPrice: 150 + id * 10,
      discountPrice: 30,
      finalPrice: 120 + id * 10,
    },
    provider: {
      id: 1,
      name: 'Test Provider',
      imageUrl: '/provider-image.jpg',
      providerUrl: '/provider/1',
      description: 'Provider description',
    },
    availableIn: ['São Paulo', 'Rio de Janeiro'],
    details: [`Detail ${id}-1`, `Detail ${id}-2`],
    serviceLimits: 'Service limits information',
    keywords: ['test', 'service'],
    categoryName: 'Test Category',
    categorySlug: 'test-category',
    subcategoryName: 'Test Subcategory',
    subcategorySlug: 'test-subcategory',
    termsConditionsUrl: '/terms',
    preparations: `Preparation ${id}-1, Preparation ${id}-2`,
  });

  // Fetch real data before running tests
  beforeAll(async () => {
    try {
      console.warn('🌐 Fetching real API data for integration tests...');

      // Make a real API call
      const apiUrl = 'https://ecommerce-bff-api-smoke.getninjas.io/api/v1/service-type/list';
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'service-provider': 'EUR',
          Accept: 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }

      const data = await response.json();

      // Process the nested API response to extract services
      if (data && data.categories) {
        data.categories.forEach((category: any) => {
          if (category.subcategories) {
            category.subcategories.forEach((subcategory: any) => {
              if (subcategory.services) {
                // Add category and subcategory info to each service
                const servicesWithCategories = subcategory.services.map((service: any) => ({
                  ...service,
                  categoryName: category.name,
                  categorySlug: category.slug,
                  subcategoryName: subcategory.name,
                  subcategorySlug: subcategory.slug,
                }));

                realApiServices.push(...servicesWithCategories);
              }
            });
          }
        });
      }

      // Use the first service for testing
      if (realApiServices.length > 0) {
        realApiService = realApiServices[0];
        console.warn(`✅ Successfully fetched ${realApiServices.length} services from API`);
        console.warn(`✅ Using service: ${realApiService.name} (ID: ${realApiService.id})`);
      } else {
        console.warn('⚠️ No services found in API response');
      }
    } catch (error) {
      console.error('❌ Error fetching API data:', error);
    }
  });

  beforeEach(() => {
    jest.clearAllMocks();

    // If real API data is available, use it
    if (realApiService && realApiServices.length > 0) {
      // Setup mock for ServiceContext with real API data
      mockUseServiceContext.mockReturnValue({
        services: [
          {
            id: 1,
            name: realApiService.categoryName,
            slug: realApiService.categorySlug,
            subcategories: [
              {
                id: 101,
                name: realApiService.subcategoryName,
                slug: realApiService.subcategorySlug,
                services: realApiServices.slice(0, 2), // Use first two services
              },
            ],
          },
        ],
      });
    } else {
      // Fallback to mock data if API call fails
      console.warn('⚠️ Using fallback mock data for tests');
      const mockService1 = createMockService(1, 'Test Service', 'test-service');
      const mockService2 = createMockService(2, 'Another Service', 'another-service');

      mockUseServiceContext.mockReturnValue({
        services: [
          {
            id: 1,
            name: 'Test Category',
            slug: 'test-category',
            subcategories: [
              {
                id: 101,
                name: 'Test Subcategory',
                slug: 'test-subcategory',
                services: [mockService1, mockService2],
              },
            ],
          },
        ],
      });

      // Use mock service if real API data is not available
      realApiService = mockService1;
    }
  });

  it('renders with real API data', async () => {
    // Skip test if no API data is available (should never happen due to fallback)
    if (!realApiService) {
      console.warn('⚠️ Skipping test due to missing API data');
      return;
    }

    // TypeScript safety - we've already checked realApiService is not null
    const service = realApiService as ServiceType;

    // Mock Image component to avoid issues with image loading
    jest.mock('next/image', () => ({
      __esModule: true,
      default: ({ src, alt, onLoad, ...props }: any) => {
        // Call onLoad immediately to simulate image loaded
        if (onLoad) {
          setTimeout(() => onLoad(), 0);
        }
        return <img src={src} alt={alt} data-testid="next-image" {...props} />;
      },
    }));

    await act(async () => {
      render(<MainCard service={service} />);
    });

    // Wait for the component to finish rendering
    await waitFor(() => {
      expect(screen.getByText(service.subcategoryName || '')).toBeInTheDocument();
    });

    // Check that the component renders with real API data
    expect(screen.getByText(service.description || '')).toBeInTheDocument();

    // Check price
    mockFormatPrice.mockImplementation((price) => `R$ ${price},00`);
    expect(screen.getByText(`R$ ${service.price.finalPrice},00`)).toBeInTheDocument();

    // Log whether we're using real or mock data
    if (realApiServices.length > 0) {
      console.warn('✅ Test running with REAL API data');
    } else {
      console.warn('⚠️ Test running with MOCK data (API unavailable)');
    }
  });

  it('handles API data structure correctly', async () => {
    // Skip test if no API data is available (should never happen due to fallback)
    if (!realApiService) {
      console.warn('⚠️ Skipping test due to missing API data');
      return;
    }

    // TypeScript safety - we've already checked realApiService is not null
    const service = realApiService as ServiceType;

    // Mock Image component to avoid issues with image loading
    jest.mock('next/image', () => ({
      __esModule: true,
      default: ({ src, alt, onLoad, ...props }: any) => {
        // Call onLoad immediately to simulate image loaded
        if (onLoad) {
          setTimeout(() => onLoad(), 0);
        }
        return <img src={src} alt={alt} data-testid="next-image" {...props} />;
      },
    }));

    await act(async () => {
      render(<MainCard service={service} />);
    });

    // Wait for the component to finish rendering and the button to be enabled
    await waitFor(() => {
      const button = screen.getByRole('button', { name: /agendar agora/i });
      expect(button).toBeInTheDocument();
      expect(button).not.toBeDisabled();
    });

    // Check that the price is formatted correctly
    mockFormatPrice.mockImplementation((price) => `R$ ${price},00`);
    const originalPrice = screen.getByText(`R$ ${service.price.originalPrice},00`);
    expect(originalPrice).toBeInTheDocument();
    expect(originalPrice.closest('p')).toHaveClass('line-through');
  });
});
